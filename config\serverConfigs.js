// Multi-server configuration for dungeon alerts and world boss alerts
// This allows the bot to work across multiple Discord servers with server-specific settings

module.exports = {
    // Default server configuration (current main server)
    '1362356687092191442': {
        name: 'RankBreaker Main Server',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1368266784532205650',
            dungeonRoles: {
                E: '1364560442910969866',
                D: '1364560380709306389',
                C: '1364560082519588875',
                B: '1364560075770826782',
                A: '1364560068133126144',
                S: '1364560060923252766',
                SS: '1364559909722652723',
                DUNGEON_PING: '1364559489604390996',
                RED_DUNGEON: '1364559668772605992',
                DOUBLE_DUNGEON: '1364559627144003645'
            },
            worldRoles: {
                1: '1380529138309529762', // World 1 ping role
                2: '1380529145938972712'  // World 2 ping role
            },
            islandRoles: {
                'Leveling City': '1371844586003103865',
                'Grass Village': '1371844603040370759',
                'Brum Island': '1371844612196532304',
                'Faceheal Town': '1371844622015402086',
                'Lucky Kingdom': '1371844631092003037',
                'Nipon City': '1371844639430021132',
                'Mori Town': '1371844649798602833',
                'Dragon City': '1371844984244015256',
                'XZ City': '1371845004598968441',
                'Kindama City': '1371845011116785674',
                'Hunters City': '1373302432389398559',
                'Nen City': '1380528415031033886'
            }
        },
        worldBossAlert: {
            enabled: true,
            targetChannelId: '1381910717573365771',
            roleId: '1381911057316057158',
            worldBossRoles: {
                'Leveling City': '1382042037716783204',  // Boto
                'Grass Village': '1382042081874411590',  // Baruto
                'Faceheal Town': '1382042094767575230',  // Baizen
                'Nipon City': '1382260491925196841',     // Force
                'Dragon City': '1382041920666206278',    // Begeta
                'Kindama City': '1382260587127373904',   // Alien
                'Nen City': '1382041840236106002'        // Gon
            }
        },
        infernalAlert: {
            enabled: true,
            targetChannelId: '1372921982483435581',
            generalAlertRole: '1372921375005737003',
            monarchAlertRole: '1373306924115824740',
            webhookName: 'RankBreaker',
            webhookAvatar: 'https://cdn.discordapp.com/icons/1362356687092191442/25b53ae035c74c9d6edcf8ca11dfc205.webp?size=1024',
            messageFormat: 'default' // 'default' or 'custom'
        }
    },

    // HeavenShadow Server Configuration
    '1360779154018275418': {
        name: 'HeavenShadow Server',
        worldBossAlert: {
            enabled: true,
            targetChannelId: '1382336193823248445',
            roleId: '1378603401952100392',
            worldBossRoles: {
                'Leveling City': '1382337013566410822',  // Boto
                'Grass Village': '1382337077793787966',  // Baruto
                'Faceheal Town': '1382337084244758609',  // Baizen
                'Nipon City': '1382337094709542994',     // Force
                'Dragon City': '1382337101898580108',    // Begeta
                'Kindama City': '1382337324402212895',   // Alien
                'Nen City': '1382337331058446337'        // Gon
            }
        },
        infernalAlert: {
            enabled: false,
            targetChannelId: '1371927743037440141', // Replace with actual channel ID
            generalAlertRole: '1382268515041677373',
            monarchAlertRole: '1371777339137986611',
            webhookName: 'HeavenShadow',
            webhookAvatar: 'https://cdn.discordapp.com/avatars/1381956982570877039/415f16c041d8a7004349f403ee2c9059.webp?size=1024',
            messageFormat: 'custom'
        }
    },

    // Example configuration for additional servers
    // Uncomment and modify as needed for more servers
    /*
    'ANOTHER_SERVER_ID_HERE': {
        name: 'Second Server Name',
        dungeonAlert: {
            enabled: true,
            targetChannelId: 'DUNGEON_CHANNEL_ID_HERE',
            dungeonRoles: {
                E: 'E_RANK_ROLE_ID',
                D: 'D_RANK_ROLE_ID',
                C: 'C_RANK_ROLE_ID',
                B: 'B_RANK_ROLE_ID',
                A: 'A_RANK_ROLE_ID',
                S: 'S_RANK_ROLE_ID',
                SS: 'SS_RANK_ROLE_ID',
                DUNGEON_PING: 'GENERAL_DUNGEON_PING_ROLE_ID',
                RED_DUNGEON: 'RED_DUNGEON_ROLE_ID',
                DOUBLE_DUNGEON: 'DOUBLE_DUNGEON_ROLE_ID'
            },
            worldRoles: {
                1: 'WORLD_1_ROLE_ID',
                2: 'WORLD_2_ROLE_ID'
            },
            islandRoles: {
                'Leveling City': 'LEVELING_CITY_ROLE_ID',
                'Grass Village': 'GRASS_VILLAGE_ROLE_ID',
                'Brum Island': 'BRUM_ISLAND_ROLE_ID',
                'Faceheal Town': 'FACEHEAL_TOWN_ROLE_ID',
                'Lucky Kingdom': 'LUCKY_KINGDOM_ROLE_ID',
                'Nipon City': 'NIPON_CITY_ROLE_ID',
                'Mori Town': 'MORI_TOWN_ROLE_ID',
                'Dragon City': 'DRAGON_CITY_ROLE_ID',
                'XZ City': 'XZ_CITY_ROLE_ID',
                'Kindama City': 'KINDAMA_CITY_ROLE_ID',
                'Hunters City': 'HUNTERS_CITY_ROLE_ID',
                'Nen City': 'NEN_CITY_ROLE_ID'
            }
        },
        worldBossAlert: {
            enabled: true,
            targetChannelId: 'WORLD_BOSS_CHANNEL_ID_HERE',
            roleId: 'WORLD_BOSS_ROLE_ID_HERE',
            worldBossRoles: {
                'Leveling City': 'BOTO_ROLE_ID',
                'Grass Village': 'BARUTO_ROLE_ID',
                'Faceheal Town': 'BAIZEN_ROLE_ID',
                'Nipon City': 'FORCE_ROLE_ID',
                'Dragon City': 'BEGETA_ROLE_ID',
                'Kindama City': 'ALIEN_ROLE_ID',
                'Nen City': 'GON_ROLE_ID'
            }
        },
        infernalAlert: {
            enabled: true,
            targetChannelId: 'INFERNAL_CHANNEL_ID_HERE',
            generalAlertRole: 'INFERNAL_GENERAL_ROLE_ID',
            monarchAlertRole: 'INFERNAL_MONARCH_ROLE_ID',
            webhookName: 'BotName',
            webhookAvatar: 'WEBHOOK_AVATAR_URL_HERE',
            messageFormat: 'default' // 'default' or 'custom'
        }
    },
    */

    // Hellspire (Auto-generated)
    '1374046574270746777': {
        name: 'Hellspire',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1377799826195877918',
            dungeonRoles: {
                E: '1382917471316742218',
                D: '1382917475091480687',
                C: '1382917477956194320',
                B: '1382917482310012940',
                A: '1382917487259156541',
                S: '1382917491113857116',
                SS: '1382917494720827434',
                DUNGEON_PING: '1382917498105626636',
                RED_DUNGEON: '1382917502119448617',
                DOUBLE_DUNGEON: '1382917506670526464'
            },
            worldRoles: {
                1: '1382917510348800122',
                2: '1382917513041678407'
            },
            islandRoles: {
                'Leveling City': '1382917516195663883',
                'Grass Village': '1382917520440430663',
                'Brum Island': '1382917524559106048',
                'Faceheal Town': '1382917528086384760',
                'Lucky Kingdom': '1382917537548865638',
                'Nipon City': '1382917543907295284',
                'Mori Town': '1382917548210917436',
                'Dragon City': '1382917551519957133',
                'XZ City': '1382917554913148929',
                'Kindama City': '1382917558331637762',
                'Hunters City': '1382917562240864326',
                'Nen City': '1382917566003154975'
            }
        }
    },


    // Six Shadows GUILD Configuration (Auto-generated)
    '1353186342682497124': {
        name: 'Six Shadows GUILD',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1379160904360267816',
            dungeonRoles: {
                E: '1382927848259452939',
                D: '1382927852160290857',
                C: '1382927856664842250',
                B: '1382927861014597684',
                A: '1382927865150181386',
                S: '1382927869310931044',
                SS: '1382927873882591373',
                DUNGEON_PING: '1382927878307713085',
                RED_DUNGEON: '1382927883697131521',
                DOUBLE_DUNGEON: '1382927888394883183'
            },
            worldRoles: {
                1: '1382927897681203243',
                2: '1382927901896212610'
            },
            islandRoles: {
                'Leveling City': '1382927905746718791',
                'Grass Village': '1382927909987160156',
                'Brum Island': '1382927914156294285',
                'Faceheal Town': '1382927918253998111',
                'Lucky Kingdom': '1382927922603626566',
                'Nipon City': '1382927926391078982',
                'Mori Town': '1382927931315191818',
                'Dragon City': '1382927935677272084',
                'XZ City': '1382927939120922679',
                'Kindama City': '1382927943256244274',
                'Hunters City': '1382927946993504286',
                'Nen City': '1382927950567047241'
            }
        }
    },


    // ApsoluteOne Configuration (Auto-generated)
    '1371212189448278087': {
        name: 'ApsoluteOne',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1382586393100025957',
            dungeonRoles: {
                E: '1383011760231026729',
                D: '1383011764014153809',
                C: '1383011768606916638',
                B: '1383011773971693588',
                A: '1383011778270855258',
                S: '1383011784633618445',
                SS: '1383011789557727285',
                DUNGEON_PING: '1383011794141974558',
                RED_DUNGEON: '1383011797451276291',
                DOUBLE_DUNGEON: '1383011802085986387'
            },
            worldRoles: {
                1: '1383011806125101126',
                2: '1383011816086569062'
            },
            islandRoles: {
                'Leveling City': '1383011820754964590',
                'Grass Village': '1383011825137881139',
                'Brum Island': '1383011829264941076',
                'Faceheal Town': '1383011833019109396',
                'Lucky Kingdom': '1383011836223422518',
                'Nipon City': '1383011841025773568',
                'Mori Town': '1383011846101012500',
                'Dragon City': '1383011850022551554',
                'XZ City': '1383011854804320379',
                'Kindama City': '1383011859115933716',
                'Hunters City': '1383011862647672877',
                'Nen City': '1383011866737115157'
            }
        }
    },

    // Shared configuration that applies to all servers
    shared: {
        // World mapping for islands (same across all servers)
        worldIslands: {
            1: ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town', 'Lucky Kingdom', 'Nipon City', 'Mori Town'],
            2: ['Dragon City', 'XZ City', 'Kindama City', 'Hunters City', 'Nen City']
        },

        // Boss names for each island (same across all servers)
        islandBosses: {
            'Leveling City': 'Vermillion',
            'Grass Village': 'Dor',
            'Brum Island': 'Mifalcon',
            'Faceheal Town': 'Murcielago',
            'Lucky Kingdom': 'Time King',
            'Nipon City': 'Chainsaw',
            'Mori Town': 'Gucci',
            'Dragon City': 'Frioo',
            'XZ City': 'Paitama',
            'Kindama City': 'Tuturum',
            'Hunters City': 'Dae In',
            'Nen City': 'God Speed'
        },

        // World boss island-boss mapping (same across all servers)
        worldBossIslandBosses: {
            'Leveling City': 'Boto',
            'Grass Village': 'Baruto',
            'Faceheal Town': 'Baizen',
            'Nipon City': 'Force',
            'Dragon City': 'Begeta',
            'Kindama City': 'Alien',
            'Nen City': 'Gon'
        },

        // Rank colors for dungeon images (same across all servers)
        rankColors: {
            'E': '#8897aa',
            'D': '#4488ff',
            'C': '#44aaff',
            'B': '#6644ff',
            'A': '#8844ff',
            'S': '#aa44ff',
            'SS': '#ff44ff'
        },

        // World boss colors for images (using rank colors from dungeon alerts)
        worldBossColors: {
            'Boto': '#8897aa',    // E rank color
            'Baruto': '#4488ff',  // D rank color
            'Baizen': '#44aaff',  // C rank color
            'Force': '#6644ff',   // B rank color
            'Begeta': '#8844ff',  // A rank color
            'Alien': '#aa44ff',   // S rank color
            'Gon': '#ff44ff'      // SS rank color
        }
    },

    // Helper function to get server configuration
    getServerConfig: function(serverId) {
        return this[serverId] || null;
    },

    // Helper function to get dungeon alert config for a server
    getDungeonConfig: function(serverId) {
        const serverConfig = this.getServerConfig(serverId);
        return serverConfig?.dungeonAlert || null;
    },

    // Helper function to get world boss alert config for a server
    getWorldBossConfig: function(serverId) {
        const serverConfig = this.getServerConfig(serverId);
        return serverConfig?.worldBossAlert || null;
    },

    // Helper function to get all enabled servers for dungeon alerts
    getEnabledDungeonServers: function() {
        const enabledServers = [];
        for (const [serverId, config] of Object.entries(this)) {
            if (typeof config === 'object' && config.dungeonAlert?.enabled) {
                enabledServers.push({
                    serverId,
                    name: config.name,
                    config: config.dungeonAlert
                });
            }
        }
        return enabledServers;
    },

    // Helper function to get all enabled servers for world boss alerts
    getEnabledWorldBossServers: function() {
        const enabledServers = [];
        for (const [serverId, config] of Object.entries(this)) {
            if (typeof config === 'object' && config.worldBossAlert?.enabled) {
                enabledServers.push({
                    serverId,
                    name: config.name,
                    config: config.worldBossAlert
                });
            }
        }
        return enabledServers;
    },

    // Helper function to get infernal alert config for a server
    getInfernalConfig: function(serverId) {
        const serverConfig = this.getServerConfig(serverId);
        return serverConfig?.infernalAlert || null;
    },

    // Helper function to get all enabled servers for infernal alerts
    getEnabledInfernalServers: function() {
        const enabledServers = [];
        for (const [serverId, config] of Object.entries(this)) {
            if (typeof config === 'object' && config.infernalAlert?.enabled) {
                enabledServers.push({
                    serverId,
                    name: config.name,
                    config: config.infernalAlert
                });
            }
        }
        return enabledServers;
    }
};
