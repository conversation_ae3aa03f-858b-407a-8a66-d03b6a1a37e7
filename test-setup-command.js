/**
 * Test script for the setup command functionality
 * Tests the auto dungeon setup and configuration saving
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Setup Command Configuration');
console.log('=====================================');

console.log('\n1️⃣ Testing serverConfigs import...');
try {
  // Clear cache first
  const configPath = path.join(__dirname, 'config/serverConfigs.js');
  delete require.cache[require.resolve('./config/serverConfigs.js')];
  
  const serverConfigs = require('./config/serverConfigs.js');
  console.log('✅ Successfully imported serverConfigs');
  console.log('📊 Available methods:', Object.getOwnPropertyNames(serverConfigs).filter(name => typeof serverConfigs[name] === 'function'));
} catch (error) {
  console.error('❌ Error importing serverConfigs:', error.message);
  process.exit(1);
}

console.log('\n2️⃣ Testing dungeon server configurations...');
try {
  const serverConfigs = require('./config/serverConfigs.js');
  const dungeonServers = serverConfigs.getEnabledDungeonServers();
  console.log(`✅ Found ${dungeonServers.length} enabled dungeon servers:`);
  
  dungeonServers.forEach(server => {
    console.log(`  📋 ${server.name} (${server.serverId}):`);
    console.log(`    - Target Channel: ${server.config.targetChannelId}`);
    console.log(`    - Dungeon Roles: ${Object.keys(server.config.dungeonRoles).length} roles`);
    console.log(`    - World Roles: ${Object.keys(server.config.worldRoles).length} roles`);
    if (server.config.islandRoles) {
      console.log(`    - Island Roles: ${Object.keys(server.config.islandRoles).length} roles`);
    } else {
      console.log(`    - Island Roles: Not configured`);
    }
  });
  
  if (dungeonServers.length === 0) {
    console.log('⚠️ No dungeon servers enabled - check serverConfigs.js');
  }
} catch (error) {
  console.error('❌ Error testing dungeon configurations:', error.message);
}

console.log('\n3️⃣ Testing role configuration structure...');
try {
  const serverConfigs = require('./config/serverConfigs.js');
  const dungeonServers = serverConfigs.getEnabledDungeonServers();
  
  dungeonServers.forEach(server => {
    console.log(`\n📋 ${server.name} role structure:`);
    
    // Check dungeon roles
    const expectedDungeonRoles = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
    const missingDungeonRoles = expectedDungeonRoles.filter(role => !server.config.dungeonRoles[role]);
    
    if (missingDungeonRoles.length === 0) {
      console.log(`  ✅ All dungeon roles configured (${expectedDungeonRoles.length})`);
    } else {
      console.log(`  ❌ Missing dungeon roles: ${missingDungeonRoles.join(', ')}`);
    }
    
    // Check world roles
    const expectedWorldRoles = ['1', '2'];
    const missingWorldRoles = expectedWorldRoles.filter(role => !server.config.worldRoles[role]);
    
    if (missingWorldRoles.length === 0) {
      console.log(`  ✅ All world roles configured (${expectedWorldRoles.length})`);
    } else {
      console.log(`  ❌ Missing world roles: ${missingWorldRoles.join(', ')}`);
    }
    
    // Check island roles
    if (server.config.islandRoles) {
      const expectedIslandRoles = [
        'Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town', 
        'Lucky Kingdom', 'Nipon City', 'Mori Town', 'Dragon City', 
        'XZ City', 'Kindama City', 'Hunters City', 'Nen City'
      ];
      const missingIslandRoles = expectedIslandRoles.filter(role => !server.config.islandRoles[role]);
      
      if (missingIslandRoles.length === 0) {
        console.log(`  ✅ All island roles configured (${expectedIslandRoles.length})`);
      } else {
        console.log(`  ❌ Missing island roles: ${missingIslandRoles.join(', ')}`);
      }
    } else {
      console.log(`  ⚠️ Island roles not configured for this server`);
    }
  });
} catch (error) {
  console.error('❌ Error testing role structure:', error.message);
}

console.log('\n4️⃣ Testing setup command role colors...');
try {
  const setupCommand = require('./commands/slashCommands/info/setup.js');
  
  const testRoles = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
  console.log('✅ Role color mapping:');
  
  testRoles.forEach(role => {
    const color = setupCommand.getRoleColor(role);
    console.log(`  ${role}: ${color}`);
  });
} catch (error) {
  console.error('❌ Error testing role colors:', error.message);
}

console.log('\n5️⃣ Configuration Summary:');
try {
  const serverConfigs = require('./config/serverConfigs.js');
  const dungeonServers = serverConfigs.getEnabledDungeonServers();
  
  console.log(`✅ Total enabled dungeon servers: ${dungeonServers.length}`);
  console.log(`✅ Setup command available: ${fs.existsSync('./commands/slashCommands/info/setup.js')}`);
  console.log(`✅ Module cache clearing: Implemented`);
  console.log(`✅ Island roles support: Added`);
  
  if (dungeonServers.length > 0) {
    console.log('\n📊 Server Details:');
    dungeonServers.forEach((server, index) => {
      console.log(`  ${index + 1}. ${server.name}`);
      console.log(`     • Server ID: ${server.serverId}`);
      console.log(`     • Channel: ${server.config.targetChannelId}`);
      console.log(`     • Roles: ${Object.keys(server.config.dungeonRoles).length} dungeon + ${Object.keys(server.config.worldRoles).length} world${server.config.islandRoles ? ' + ' + Object.keys(server.config.islandRoles).length + ' island' : ''}`);
    });
  }
} catch (error) {
  console.error('❌ Error in configuration summary:', error.message);
}

console.log('\n🎯 Test Complete!');
console.log('================');
console.log('If you see any ❌ errors above, those need to be addressed.');
console.log('If all tests show ✅, the setup command should work correctly.');
