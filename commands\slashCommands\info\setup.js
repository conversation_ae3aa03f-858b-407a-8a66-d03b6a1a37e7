const { <PERSON><PERSON><PERSON><PERSON>mandBuilder, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');
const serverConfigs = require('../../../config/serverConfigs.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup')
        .setDescription('Setup server configurations')
        .addSubcommand(subcommand =>
            subcommand
                .setName('auto_dungeons')
                .setDescription('Set up Arise Crossover game\'s auto dungeons notifier')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('The channel where dungeon alerts will be posted')
                        .setRequired(true)))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),

    name: 'setup',
    category: 'admin',
    aliases: [],
    cooldown: 30,
    usage: '/setup auto_dungeons channel:<channel>',
    description: 'Set up server configurations for various bot features',
    memberpermissions: ['ManageGuild'],
    botpermissions: ['ManageRoles'],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: true,
    DevloperTeamOnly: false,

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        if (subcommand === 'auto_dungeons') {
            await this.setupAutoDungeons(interaction);
        }
    },

    async setupAutoDungeons(interaction) {
        try {
            await interaction.deferReply({ ephemeral: true });

            const channel = interaction.options.getChannel('channel');
            const guild = interaction.guild;
            const serverId = guild.id;

            // Check if server already has dungeon configuration
            const existingConfig = serverConfigs.getDungeonConfig(serverId);
            if (existingConfig && existingConfig.enabled) {
                return await interaction.editReply({
                    content: '❌ This server already has dungeon alerts configured. Only one dungeon channel per server is allowed.',
                    ephemeral: true
                });
            }

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setTitle('🔧 Setting up Auto Dungeons')
                .setDescription('Setting up dungeon alert system...')
                .setColor('#3498db')
                .addFields(
                    { name: '📍 Target Channel', value: `${channel}`, inline: true },
                    { name: '⚙️ Status', value: '🔄 Creating roles...', inline: true }
                );

            await interaction.editReply({ embeds: [progressEmbed] });

            // Define all required dungeon roles
            const dungeonRoleNames = {
                E: 'E Dungeon Ping',
                D: 'D Dungeon Ping', 
                C: 'C Dungeon Ping',
                B: 'B Dungeon Ping',
                A: 'A Dungeon Ping',
                S: 'S Dungeon Ping',
                SS: 'SS Dungeon Ping',
                DUNGEON_PING: 'Dungeons Ping',
                RED_DUNGEON: 'Red Gate Ping',
                DOUBLE_DUNGEON: 'Double Dungeon Ping'
            };

            const worldRoleNames = {
                1: 'World 1 Ping',
                2: 'World 2 Ping'
            };

            const createdRoles = {};
            const createdWorldRoles = {};
            let roleCount = 0;
            const totalRoles = Object.keys(dungeonRoleNames).length + Object.keys(worldRoleNames).length;

            // Create dungeon roles
            for (const [key, roleName] of Object.entries(dungeonRoleNames)) {
                try {
                    // Check if role already exists
                    let existingRole = guild.roles.cache.find(role => role.name === roleName);
                    
                    if (!existingRole) {
                        // Create the role
                        const newRole = await guild.roles.create({
                            name: roleName,
                            color: this.getRoleColor(key),
                            mentionable: true,
                            reason: 'Auto-setup for dungeon alerts'
                        });
                        createdRoles[key] = newRole.id;
                        console.log(`Created role: ${roleName} (${newRole.id})`);
                    } else {
                        createdRoles[key] = existingRole.id;
                        console.log(`Using existing role: ${roleName} (${existingRole.id})`);
                    }
                    
                    roleCount++;
                    
                    // Update progress
                    progressEmbed.setFields(
                        { name: '📍 Target Channel', value: `${channel}`, inline: true },
                        { name: '⚙️ Status', value: `🔄 Creating roles... (${roleCount}/${totalRoles})`, inline: true }
                    );
                    await interaction.editReply({ embeds: [progressEmbed] });
                    
                } catch (error) {
                    console.error(`Error creating role ${roleName}:`, error);
                }
            }

            // Create world roles
            for (const [worldNum, roleName] of Object.entries(worldRoleNames)) {
                try {
                    // Check if role already exists
                    let existingRole = guild.roles.cache.find(role => role.name === roleName);
                    
                    if (!existingRole) {
                        // Create the role
                        const newRole = await guild.roles.create({
                            name: roleName,
                            color: '#7289da', // Discord blurple
                            mentionable: true,
                            reason: 'Auto-setup for dungeon alerts'
                        });
                        createdWorldRoles[worldNum] = newRole.id;
                        console.log(`Created world role: ${roleName} (${newRole.id})`);
                    } else {
                        createdWorldRoles[worldNum] = existingRole.id;
                        console.log(`Using existing world role: ${roleName} (${existingRole.id})`);
                    }
                    
                    roleCount++;
                    
                    // Update progress
                    progressEmbed.setFields(
                        { name: '📍 Target Channel', value: `${channel}`, inline: true },
                        { name: '⚙️ Status', value: `🔄 Creating roles... (${roleCount}/${totalRoles})`, inline: true }
                    );
                    await interaction.editReply({ embeds: [progressEmbed] });
                    
                } catch (error) {
                    console.error(`Error creating world role ${roleName}:`, error);
                }
            }

            // Update progress to saving configuration
            progressEmbed.setFields(
                { name: '📍 Target Channel', value: `${channel}`, inline: true },
                { name: '⚙️ Status', value: '💾 Saving configuration...', inline: true }
            );
            await interaction.editReply({ embeds: [progressEmbed] });

            // Save configuration to serverConfigs.js
            await this.saveServerConfig(serverId, guild.name, channel.id, createdRoles, createdWorldRoles);

            // Create success embed
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Auto Dungeons Setup Complete!')
                .setDescription('Dungeon alert system has been successfully configured for this server.')
                .setColor('#2ecc71')
                .addFields(
                    { name: '📍 Alert Channel', value: `${channel}`, inline: true },
                    { name: '🎭 Roles Created', value: `${roleCount} roles`, inline: true },
                    { name: '🔔 Status', value: 'Active & Ready', inline: true }
                )
                .setFooter({ text: 'Dungeon alerts will now be posted to the configured channel' })
                .setTimestamp();

            await interaction.editReply({ embeds: [successEmbed] });

        } catch (error) {
            console.error('Error setting up auto dungeons:', error);
            await interaction.editReply({
                content: '❌ An error occurred while setting up auto dungeons. Please try again or contact support.',
                ephemeral: true
            });
        }
    },

    async saveServerConfig(serverId, serverName, channelId, dungeonRoles, worldRoles) {
        const configPath = path.join(__dirname, '../../../config/serverConfigs.js');
        
        // Read current config
        let configContent = fs.readFileSync(configPath, 'utf8');
        
        // Create new server configuration
        const newServerConfig = `
    // ${serverName} Configuration (Auto-generated)
    '${serverId}': {
        name: '${serverName}',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '${channelId}',
            dungeonRoles: {
                E: '${dungeonRoles.E}',
                D: '${dungeonRoles.D}',
                C: '${dungeonRoles.C}',
                B: '${dungeonRoles.B}',
                A: '${dungeonRoles.A}',
                S: '${dungeonRoles.S}',
                SS: '${dungeonRoles.SS}',
                DUNGEON_PING: '${dungeonRoles.DUNGEON_PING}',
                RED_DUNGEON: '${dungeonRoles.RED_DUNGEON}',
                DOUBLE_DUNGEON: '${dungeonRoles.DOUBLE_DUNGEON}'
            },
            worldRoles: {
                1: '${worldRoles[1]}',
                2: '${worldRoles[2]}'
            }
        }
    },`;

        // Find the position to insert the new config (before the shared section)
        const sharedIndex = configContent.indexOf('    // Shared configuration');
        if (sharedIndex !== -1) {
            configContent = configContent.slice(0, sharedIndex) + newServerConfig + '\n\n' + configContent.slice(sharedIndex);
        } else {
            // Fallback: insert before the closing of module.exports
            const moduleExportsEnd = configContent.lastIndexOf('};');
            configContent = configContent.slice(0, moduleExportsEnd) + newServerConfig + '\n\n' + configContent.slice(moduleExportsEnd);
        }

        // Write updated config
        fs.writeFileSync(configPath, configContent);
        console.log(`Saved server configuration for ${serverName} (${serverId})`);
    },

    getRoleColor(roleKey) {
        const colors = {
            E: '#8897aa',      // Gray
            D: '#4488ff',      // Blue  
            C: '#44aaff',      // Light Blue
            B: '#6644ff',      // Purple
            A: '#8844ff',      // Dark Purple
            S: '#aa44ff',      // Magenta
            SS: '#ff44ff',     // Pink
            DUNGEON_PING: '#ffa500',     // Orange
            RED_DUNGEON: '#ff4444',      // Red
            DOUBLE_DUNGEON: '#44ff44'    // Green
        };
        return colors[roleKey] || '#7289da'; // Default Discord blurple
    }
};
