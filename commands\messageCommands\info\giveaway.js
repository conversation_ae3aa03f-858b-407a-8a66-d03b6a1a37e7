const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../../../config/config.js');

module.exports = {
    name: 'giveaway',
    category: 'info',
    aliases: ['gstart', 'startgiveaway'],
    cooldown: 60,
    usage: '<prize> <duration>',
    description: 'Start a giveaway',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    async execute(message, args) {
        const prize = args.join(' ') || 'Amazing Prize';
        const giveawayId = `giveaway_${Date.now()}`;
        const giveawayFile = path.join(__dirname, '../../../data/giveaways.json');

        // Ensure data directory exists
        const dataDir = path.dirname(giveawayFile);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // Initialize giveaway data
        let giveaways = {};
        if (fs.existsSync(giveawayFile)) {
            try {
                giveaways = JSON.parse(fs.readFileSync(giveawayFile, 'utf8'));
            } catch (error) {
                giveaways = {};
            }
        }

        giveaways[giveawayId] = {
            prize: prize,
            host: message.author.id,
            channelId: message.channel.id,
            messageId: null,
            entries: [],
            active: true,
            startTime: Date.now()
        };

        // Create embed
        const embed = new EmbedBuilder()
            .setTitle('🎉 RankBreaker\'s Giveaway 🎉')
            .setDescription(`**Prize:** ${prize}\n\n**How to Enter:**\nClick the 🎁 button below to enter!\n\n**Entries:** 0\n\n**Status:** Active`)
            .setColor('#3498db')
            .setFooter({ text: `Giveaway ID: ${giveawayId}`, iconURL: message.client.user.displayAvatarURL() })
            .setTimestamp()
            .setThumbnail('https://cdn.discordapp.com/emojis/742043142750388304.png');

        // Create button
        const button = new ButtonBuilder()
            .setCustomId(`giveaway_enter_${giveawayId}`)
            .setLabel('🎁 Enter Giveaway')
            .setStyle(ButtonStyle.Primary);

        const row = new ActionRowBuilder().addComponents(button);

        // Send message
        const giveawayMessage = await message.channel.send({ 
            embeds: [embed], 
            components: [row] 
        });

        // Update giveaway data with message ID
        giveaways[giveawayId].messageId = giveawayMessage.id;
        fs.writeFileSync(giveawayFile, JSON.stringify(giveaways, null, 2));

        // Delete command message
        if (message.deletable) {
            message.delete().catch(() => {});
        }
    },
};