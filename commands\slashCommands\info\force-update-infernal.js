const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const serverConfigs = require('../../../config/serverConfigs.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('force-update')
    .setDescription('Force update infernal castle floors across all configured servers')
    .addIntegerOption(option =>
      option.setName('floor')
        .setDescription('Floor number (30-70)')
        .setRequired(true)
        .setMinValue(30)
        .setMaxValue(70))
    .addStringOption(option =>
      option.setName('bossname')
        .setDescription('Boss name')
        .setRequired(true)
        .addChoices(
          { name: 'Vermillion', value: '<:Vermillion_RB:1374378486806089728> Vermillion' },
          { name: 'Dor', value: '<:Dor_RB:1374381617551900743> Dor' },
          { name: '<PERSON><PERSON>l<PERSON>', value: '<:Mifalcon_RB:1374379322646728704> Mi<PERSON>lcon' },
          { name: '<PERSON><PERSON><PERSON><PERSON>', value: '<:Murcielago_RB:1374436782828949524> Murcielago' },
          { name: 'Time King', value: '<:TimeKing_RB:1374436779964498020> Time King' },
          { name: 'Chainsaw', value: '<:Chainsaw_RB:1374436785953964112> Chainsaw' },
          { name: 'Gucci', value: '<:Gucci_RB:1374436776944472116> Gucci' },
          { name: 'Frioo', value: '<:Frioo_RB:1374726273238302721> Frioo' },
          { name: 'Paitama', value: '<:Paitama_RB:1374761821839425689> Paitama' },
          { name: 'Tuturum', value: '<:Tuturum_RB:1374761818286985256> Tuturum' },
          { name: 'Dae In', value: '<:DaeIn_RB:1374761806094143659> Dae In' },
          { name: 'Wesil', value: '<:Wesil_RB:1374761811726958592> Wesil' },
          { name: 'Magma', value: '<:Magma_RB:1374761801799176302> Magma' },
          { name: 'Monarch', value: '<:Monarch_RB:1374761815401037987> Monarch' }
        ))
    .addStringOption(option =>
      option.setName('message_id')
        .setDescription('Optional: Specific message ID to update (if not provided, updates latest messages)')
        .setRequired(false))
    .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages),
  name: 'force-update',
  category: 'Info',
  aliases: [],
  cooldown: 10,
  usage: 'force-update floor: bossname:',
  description: 'Force update infernal castle floors across all configured servers',
  memberpermissions: [],
  botpermissions: [],
  requiredroles: [],
  requiredchannels: [],
  alloweduserids: [],
  minargs: 2,
  maxargs: 2,
  nsfw: false,
  OwnerOnly: true,
  ServerOwnerOnly: false,
  DevloperTeamOnly: false,

  // Helper function to format message for a specific server
  formatMessageForServer(floorBossData, serverConfig) {
    const majorityBosses = this.getMajorityBosses(floorBossData);
    const floors = Object.keys(majorityBosses).map(Number).sort((a, b) => a - b);

    if (serverConfig.messageFormat === 'custom') {
      return this.formatCustomMessage(floors, majorityBosses, serverConfig);
    } else {
      return this.formatDefaultMessage(floors, majorityBosses, serverConfig);
    }
  },

  // Get majority bosses for each floor
  getMajorityBosses(floorBossData) {
    const result = {};

    Object.keys(floorBossData).forEach(floor => {
      const bosses = floorBossData[floor];
      let majorityBoss = null;
      let maxCount = 0;

      Object.keys(bosses).forEach(boss => {
        if (bosses[boss].count > maxCount) {
          maxCount = bosses[boss].count;
          majorityBoss = boss;
        }
      });

      if (maxCount >= 1) {
        result[floor] = {
          name: majorityBoss,
          count: maxCount
        };
      }
    });

    return result;
  },

  // Default message format
  formatDefaultMessage(floors, majorityBosses, serverConfig) {
    let message = '# __**INFERNAL CASTLE SPAWNED**__\n───────────────────────────────\n';

    if (floors.length === 0) {
      message += '> No confirmed floors yet... please wait.';
    } else {
      floors.forEach(floor => {
        message += `> **\`FLOOR ${floor}\`** - **${majorityBosses[floor].name}**\n`;
      });
    }

    message += `───────────────────────────────\n-# *Last updated: <t:${Math.floor(Date.now() / 1000)}:R>*\n`;
    message += `-# ***By ${serverConfig.webhookName} | .gg/M6e3PGRtd3***\n────────── <@&${serverConfig.generalAlertRole}> ──────────`;

    return message;
  },

  // Custom message format for HeavenShadow server
  formatCustomMessage(floors, majorityBosses, serverConfig) {
    let message = '';

    if (floors.length === 0) {
      message += '`No confirmed floors yet...`\n';
    } else {
      floors.forEach(floor => {
        // Remove the emoji prefix from boss name for cleaner look
        let bossName = majorityBosses[floor].name;
        // Extract just the boss name without the emoji
        const bossNameMatch = bossName.match(/>\s*(.+)$/);
        if (bossNameMatch) {
          bossName = bossNameMatch[1];
        }
        message += `\`Floor ${floor} - ${bossName}\`\n`;
      });
    }

    message += '𝓣𝓸 𝔂𝓸𝓾 𝓯𝓻𝓸𝓶 𝓽𝓱𝓮 𝓭𝓮𝓹𝓽𝓱𝓼 𝓸𝓯 𝓽𝓱𝓮 𝓓𝓪𝓻𝓴𝓷𝓮𝓼𝓼\n';
    message += '𝕓𝕪 ℍ𝕖𝕒𝕧𝕖𝕟𝕊𝕙𝕒𝕕𝕠𝕨 | /heavenshadow\n';
    message += `<@&${serverConfig.generalAlertRole}>`;

    return message;
  },

  async execute(interaction) {
    try {
      await interaction.deferReply({ ephemeral: true });

      const floor = interaction.options.getInteger('floor');
      const bossName = interaction.options.getString('bossname');
      const specificMessageId = interaction.options.getString('message_id');

      // Get all enabled infernal servers
      const enabledServers = serverConfigs.getEnabledInfernalServers();

      if (enabledServers.length === 0) {
        return await interaction.editReply('❌ No infernal servers are configured. Please check serverConfigs.js');
      }

      const displayBossName = bossName.match(/>\s*(.+)$/)?.[1] || bossName;

      if (specificMessageId) {
        // Update specific message ID across all servers
        await interaction.editReply(`🔄 Updating specific message ${specificMessageId} with Floor ${floor} - ${displayBossName}...`);

        let successCount = 0;
        let errorCount = 0;
        const results = [];

        for (const server of enabledServers) {
          try {
            const channel = await interaction.client.channels.fetch(server.config.targetChannelId);
            const message = await channel.messages.fetch(specificMessageId);

            // Create temporary floor data for formatting
            const tempFloorData = {
              [floor]: {
                [bossName]: {
                  count: 1,
                  users: ['manual_update']
                }
              }
            };

            // Format message according to server's format
            const formattedMessage = this.formatMessageForServer(tempFloorData, server.config);

            await message.edit(formattedMessage);
            results.push(`✅ ${server.name}: Updated successfully`);
            successCount++;

          } catch (error) {
            results.push(`❌ ${server.name}: ${error.message}`);
            errorCount++;
          }
        }

        const resultText = results.join('\n');
        return await interaction.editReply(
          `📊 **Update Results for Message ${specificMessageId}:**\n\n` +
          `✅ **Successful:** ${successCount} servers\n` +
          `❌ **Failed:** ${errorCount} servers\n\n` +
          `**Details:**\n${resultText}`
        );

      } else {
        // Update latest messages automatically (works anytime)
        await interaction.editReply(`🔄 Finding and updating latest infernal messages with Floor ${floor} - ${displayBossName}...`);

        let successCount = 0;
        let errorCount = 0;
        const results = [];

        for (const server of enabledServers) {
          try {
            const channel = await interaction.client.channels.fetch(server.config.targetChannelId);

            // Find the latest infernal message from the webhook
            const messages = await channel.messages.fetch({ limit: 50 });
            const webhookMessage = messages.find(msg =>
              msg.webhookId &&
              msg.author.username === server.config.webhookName &&
              (msg.content.includes('INFERNAL CASTLE') || msg.content.includes('Floor') || msg.content.includes('𝓣𝓸 𝔂𝓸𝓾'))
            );

            if (!webhookMessage) {
              results.push(`⚠️ ${server.name}: No recent infernal message found`);
              errorCount++;
              continue;
            }

            // Create temporary floor data for formatting
            const tempFloorData = {
              [floor]: {
                [bossName]: {
                  count: 1,
                  users: ['manual_update']
                }
              }
            };

            // Format message according to server's format
            const formattedMessage = this.formatMessageForServer(tempFloorData, server.config);

            await webhookMessage.edit(formattedMessage);
            results.push(`✅ ${server.name}: Updated message ${webhookMessage.id}`);
            successCount++;

          } catch (error) {
            results.push(`❌ ${server.name}: ${error.message}`);
            errorCount++;
          }
        }

        const resultText = results.join('\n');
        return await interaction.editReply(
          `📊 **Auto-Update Results:**\n\n` +
          `✅ **Successful:** ${successCount} servers\n` +
          `❌ **Failed:** ${errorCount} servers\n\n` +
          `🎯 **Updated Floor ${floor} with ${displayBossName}**\n\n` +
          `**Details:**\n${resultText}\n\n` +
          `💡 **Tip:** This command works anytime, not just during monitoring hours!`
        );
      }

    } catch (error) {
      console.error('Error in force-update command:', error);

      const errorMessage = `❌ An error occurred: ${error.message}`;

      try {
        if (interaction.deferred) {
          await interaction.editReply(errorMessage);
        } else if (!interaction.replied) {
          await interaction.reply({ content: errorMessage, ephemeral: true });
        }
      } catch (replyError) {
        console.error('Could not send error response:', replyError);
      }
    }
  },
};