const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../../../config/config.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('gentries')
        .setDescription('View all entries for a specific giveaway')
        .addStringOption(option =>
            option.setName('giveaway_id')
                .setDescription('The ID of the giveaway to view entries for')
                .setRequired(true)
        ),
    name: 'gentries',
    category: 'info',
    aliases: ['giveawayentries', 'viewentries'],
    cooldown: 10,
    usage: '/gentries giveaway_id:<giveaway_id>',
    description: 'View all entries for a specific giveaway',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    async execute(interaction) {
        const giveawayId = interaction.options.getString('giveaway_id');
        const giveawayFile = path.join(__dirname, '../../../data/giveaways.json');

        // Check if giveaway file exists
        if (!fs.existsSync(giveawayFile)) {
            return interaction.reply({ content: '❌ No giveaways found!', ephemeral: true });
        }

        let giveaways = {};
        try {
            giveaways = JSON.parse(fs.readFileSync(giveawayFile, 'utf8'));
        } catch (error) {
            return interaction.reply({ content: '❌ Error reading giveaway data!', ephemeral: true });
        }

        // Check if giveaway exists
        if (!giveaways[giveawayId]) {
            return interaction.reply({ content: '❌ Giveaway not found! Use a valid giveaway ID.', ephemeral: true });
        }

        const giveaway = giveaways[giveawayId];

        // Check if user is the host or has permission
        if (giveaway.host !== interaction.user.id && !interaction.member.permissions.has('Administrator')) {
            return interaction.reply({ content: '❌ You can only view entries for giveaways that you hosted!', ephemeral: true });
        }

        // Check if there are entries
        if (giveaway.entries.length === 0) {
            return interaction.reply({ 
                content: `❌ No entries found for giveaway **${giveawayId}** (Prize: ${giveaway.prize})`, 
                ephemeral: true 
            });
        }

        // Create embed
        const embed = new EmbedBuilder()
            .setTitle(`🎫 Giveaway Entries`)
            .setColor('#3498db')
            .setTimestamp()
            .setFooter({ text: `Giveaway ID: ${giveawayId}`, iconURL: interaction.client.user.displayAvatarURL() });

        let description = `**Prize:** ${giveaway.prize}\n`;
        description += `**Status:** ${giveaway.active ? 'Active' : 'Ended'}\n`;
        description += `**Total Entries:** ${giveaway.entries.length}\n\n`;
        description += `**Participants:**\n`;

        // Get user information for each entry
        const entryPromises = giveaway.entries.map(async (userId, index) => {
            try {
                const user = await interaction.client.users.fetch(userId);
                return `${index + 1}. ${user.username} (<@${userId}>)`;
            } catch (error) {
                return `${index + 1}. Unknown User (<@${userId}>)`;
            }
        });

        const entryList = await Promise.all(entryPromises);
        description += entryList.join('\n');

        // If description is too long, split it
        if (description.length > 4096) {
            const basicInfo = `**Prize:** ${giveaway.prize}\n**Status:** ${giveaway.active ? 'Active' : 'Ended'}\n**Total Entries:** ${giveaway.entries.length}\n\n`;
            const maxEntries = Math.floor((4096 - basicInfo.length - 100) / 50); // Rough estimate
            
            description = basicInfo + `**Participants (showing first ${maxEntries}):**\n`;
            description += entryList.slice(0, maxEntries).join('\n');
            
            if (giveaway.entries.length > maxEntries) {
                description += `\n\n*... and ${giveaway.entries.length - maxEntries} more entries*`;
            }
        }

        embed.setDescription(description);

        return interaction.reply({ embeds: [embed], ephemeral: true });
    },
};
