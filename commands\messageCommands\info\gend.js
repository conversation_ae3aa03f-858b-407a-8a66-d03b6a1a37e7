const { EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../../../config/config.js');

module.exports = {
    name: 'gend',
    category: 'info',
    aliases: ['endgiveaway', 'giveawayend'],
    cooldown: 30,
    usage: '<giveaway_id>',
    description: '',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    async execute(message, args) {
        const giveawayId = args[0];
        const giveawayFile = path.join(__dirname, '../../../data/giveaways.json');

        // Check if giveaway file exists
        if (!fs.existsSync(giveawayFile)) {
            return message.reply('❌ No giveaways found!');
        }

        let giveaways = {};
        try {
            giveaways = JSON.parse(fs.readFileSync(giveawayFile, 'utf8'));
        } catch (error) {
            return message.reply('❌ Error reading giveaway data!');
        }

        // Check if giveaway exists
        if (!giveaways[giveawayId]) {
            return message.reply('❌ Giveaway not found! Use a valid giveaway ID.');
        }

        const giveaway = giveaways[giveawayId];

        // Check if user is the host or has permission
        if (giveaway.host !== message.author.id && !message.member.permissions.has('Administrator')) {
            return message.reply('❌ You can only end giveaways that you hosted!');
        }

        // Check if giveaway is already ended
        if (!giveaway.active) {
            return message.reply('❌ This giveaway has already ended!');
        }

        // Check if there are entries
        if (giveaway.entries.length === 0) {
            // End giveaway with no winner
            giveaways[giveawayId].active = false;
            fs.writeFileSync(giveawayFile, JSON.stringify(giveaways, null, 2));

            const noWinnerEmbed = new EmbedBuilder()
                .setTitle('🎉 Giveaway Ended')
                .setDescription(`**Prize:** ${giveaway.prize}\n\n**Winner:** No valid entries\n\n**Total Entries:** 0`)
                .setColor('#e74c3c')
                .setFooter({ text: `Giveaway ID: ${giveawayId}`, iconURL: message.client.user.displayAvatarURL() })
                .setTimestamp();

            // Update original message
            try {
                const channel = message.client.channels.cache.get(giveaway.channelId);
                const giveawayMessage = await channel.messages.fetch(giveaway.messageId);
                await giveawayMessage.edit({ embeds: [noWinnerEmbed], components: [] });
            } catch (error) {
                console.error('Error updating giveaway message:', error);
            }

            return message.reply('✅ Giveaway ended with no winner (no entries).');
        }

        // Pick random winner
        const randomIndex = Math.floor(Math.random() * giveaway.entries.length);
        const winnerId = giveaway.entries[randomIndex];

        // End the giveaway
        giveaways[giveawayId].active = false;
        giveaways[giveawayId].winner = winnerId;
        giveaways[giveawayId].endTime = Date.now();
        fs.writeFileSync(giveawayFile, JSON.stringify(giveaways, null, 2));

        // Get winner user
        let winner;
        try {
            winner = await message.client.users.fetch(winnerId);
        } catch (error) {
            winner = { username: 'Unknown User', id: winnerId };
        }

        // Create winner embed
        const winnerEmbed = new EmbedBuilder()
            .setTitle('🎉 Giveaway Ended! 🎉')
            .setDescription(`**Prize:** ${giveaway.prize}\n\n**Winner:** <@${winnerId}> (${winner.username})\n\n**Total Entries:** ${giveaway.entries.length}`)
            .setColor('#2ecc71')
            .setFooter({ text: `Giveaway ID: ${giveawayId}`, iconURL: message.client.user.displayAvatarURL() })
            .setTimestamp();

        // Update original giveaway message
        try {
            const channel = message.client.channels.cache.get(giveaway.channelId);
            const giveawayMessage = await channel.messages.fetch(giveaway.messageId);
            await giveawayMessage.edit({ embeds: [winnerEmbed], components: [] });
        } catch (error) {
            console.error('Error updating giveaway message:', error);
        }

        // Send confirmation to command user (private)
        const privateEmbed = new EmbedBuilder()
            .setTitle('🔒 Giveaway Ended Successfully')
            .setDescription(`**Giveaway ID:** ${giveawayId}\n**Prize:** ${giveaway.prize}\n**Winner Selected:** <@${winnerId}> (${winner.username})\n**Winner ID:** ${winnerId}\n**Total Entries:** ${giveaway.entries.length}`)
            .setColor('#3498db')
            .setFooter({ text: 'This message is only visible to you', iconURL: message.client.user.displayAvatarURL() })
            .setTimestamp();

        // Send private confirmation and delete command
        await message.reply({ embeds: [privateEmbed] });
        
        // Delete the command message after a short delay
        setTimeout(() => {
            if (message.deletable) {
                message.delete().catch(() => {});
            }
        }, 2000);

        // Announce winner in the channel
        setTimeout(async () => {
            const announcementEmbed = new EmbedBuilder()
                .setTitle('🎊 Congratulations! 🎊')
                .setDescription(`<@${winnerId}> has won **${giveaway.prize}**!\n\nThank you to everyone who participated!`)
                .setColor('#f39c12')
                .setThumbnail(winner.displayAvatarURL ? winner.displayAvatarURL() : null)
                .setTimestamp();

            await message.channel.send({ content: `🎉 <@${winnerId}>`, embeds: [announcementEmbed] });
        }, 3000);
    },
};