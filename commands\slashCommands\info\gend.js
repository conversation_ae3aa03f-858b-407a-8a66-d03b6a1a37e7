const { <PERSON>lash<PERSON>ommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../../../config/config.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('gend')
        .setDescription('End a giveaway and manually select a winner')
        .addStringOption(option =>
            option.setName('giveaway_id')
                .setDescription('The ID of the giveaway to end')
                .setRequired(true)
        )
        .addUserOption(option =>
            option.setName('winner')
                .setDescription('The user to select as the winner')
                .setRequired(false)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages),
    name: 'gend',
    category: 'info',
    aliases: ['endgiveaway', 'giveawayend'],
    cooldown: 30,
    usage: '/gend giveaway_id:<giveaway_id> winner:[user]',
    description: 'End a giveaway and manually select a winner (Host only)',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: true,
    DevloperTeamOnly: false,
    async execute(interaction) {
        const giveawayId = interaction.options.getString('giveaway_id');
        const selectedWinner = interaction.options.getUser('winner');
        const giveawayFile = path.join(__dirname, '../../../data/giveaways.json');

        // Check if giveaway file exists
        if (!fs.existsSync(giveawayFile)) {
            return interaction.reply({ content: '❌ No giveaways found!', ephemeral: true });
        }

        let giveaways = {};
        try {
            giveaways = JSON.parse(fs.readFileSync(giveawayFile, 'utf8'));
        } catch (error) {
            return interaction.reply({ content: '❌ Error reading giveaway data!', ephemeral: true });
        }

        // Check if giveaway exists
        if (!giveaways[giveawayId]) {
            return interaction.reply({ content: '❌ Giveaway not found! Use a valid giveaway ID.', ephemeral: true });
        }

        const giveaway = giveaways[giveawayId];

        // Check if user is the host or has permission
        if (giveaway.host !== interaction.user.id && !interaction.member.permissions.has('Administrator')) {
            return interaction.reply({ content: '❌ You can only end giveaways that you hosted!', ephemeral: true });
        }

        // Check if giveaway is already ended
        if (!giveaway.active) {
            return interaction.reply({ content: '❌ This giveaway has already ended!', ephemeral: true });
        }

        // Handle manual winner selection
        let winnerId;

        if (selectedWinner) {
            // Manual winner selection
            winnerId = selectedWinner.id;

            // Check if the selected winner actually entered the giveaway
            if (!giveaway.entries.includes(winnerId)) {
                return interaction.reply({
                    content: `❌ ${selectedWinner.username} did not enter this giveaway! Please select someone who entered.`,
                    ephemeral: true
                });
            }
        } else {
            // No winner specified - end without winner
            if (giveaway.entries.length === 0) {
                // End giveaway with no winner - no entries
                giveaways[giveawayId].active = false;
                fs.writeFileSync(giveawayFile, JSON.stringify(giveaways, null, 2));

                const noWinnerEmbed = new EmbedBuilder()
                    .setTitle('🎉 Giveaway Ended')
                    .setDescription(`**Prize:** ${giveaway.prize}\n\n**Winner:** No entries\n\n**Total Entries:** 0`)
                    .setColor('#e74c3c')
                    .setFooter({ text: `Giveaway ID: ${giveawayId}`, iconURL: interaction.client.user.displayAvatarURL() })
                    .setTimestamp();

                // Update original message
                try {
                    const channel = interaction.client.channels.cache.get(giveaway.channelId);
                    const giveawayMessage = await channel.messages.fetch(giveaway.messageId);
                    await giveawayMessage.edit({ embeds: [noWinnerEmbed], components: [] });
                } catch (error) {
                    console.error('Error updating giveaway message:', error);
                }

                return interaction.reply({ content: '✅ Giveaway ended with no winner (no entries).', ephemeral: true });
            } else {
                // Has entries but no winner specified
                return interaction.reply({
                    content: `❌ Please specify a winner! This giveaway has ${giveaway.entries.length} entries. Use the winner parameter to select someone.`,
                    ephemeral: true
                });
            }
        }

        // End the giveaway
        giveaways[giveawayId].active = false;
        giveaways[giveawayId].winner = winnerId;
        giveaways[giveawayId].endTime = Date.now();
        fs.writeFileSync(giveawayFile, JSON.stringify(giveaways, null, 2));

        // Get winner user
        let winner;
        try {
            winner = await interaction.client.users.fetch(winnerId);
        } catch (error) {
            winner = { username: 'Unknown User', id: winnerId };
        }

        // Create winner embed
        const winnerEmbed = new EmbedBuilder()
            .setTitle('🎉 Giveaway Ended! 🎉')
            .setDescription(`**Prize:** ${giveaway.prize}\n\n**Winner:** <@${winnerId}> (${winner.username})\n\n**Total Entries:** ${giveaway.entries.length}\n\n**Selected by:** <@${interaction.user.id}>`)
            .setColor('#2ecc71')
            .setFooter({ text: `Giveaway ID: ${giveawayId}`, iconURL: interaction.client.user.displayAvatarURL() })
            .setTimestamp();

        // Update original giveaway message
        try {
            const channel = interaction.client.channels.cache.get(giveaway.channelId);
            const giveawayMessage = await channel.messages.fetch(giveaway.messageId);
            await giveawayMessage.edit({ embeds: [winnerEmbed], components: [] });
        } catch (error) {
            console.error('Error updating giveaway message:', error);
        }

        // Send announcement in the channel
        const announcementEmbed = new EmbedBuilder()
            .setTitle('🎉 Giveaway Winner! 🎉')
            .setDescription(`Congratulations <@${winnerId}>! You won **${giveaway.prize}**!\n\n*Winner selected by <@${interaction.user.id}>*`)
            .setColor('#2ecc71')
            .setTimestamp();

        try {
            const channel = interaction.client.channels.cache.get(giveaway.channelId);
            await channel.send({ embeds: [announcementEmbed] });
        } catch (error) {
            console.error('Error sending winner announcement:', error);
        }

        return interaction.reply({ 
            content: `✅ Giveaway ended! Winner: <@${winnerId}> (${winner.username})`, 
            ephemeral: true 
        });
    },
};
