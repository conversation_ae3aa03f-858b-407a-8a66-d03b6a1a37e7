/**
 * Debug script for dungeon alert posting issues
 * This will help identify why alerts aren't posting to newly configured servers
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging Dungeon Alert Issues');
console.log('==================================');

console.log('\n1️⃣ Testing serverConfigs loading...');
try {
  // Clear cache to ensure fresh load
  delete require.cache[require.resolve('./config/serverConfigs.js')];
  const serverConfigs = require('./config/serverConfigs.js');
  
  console.log('✅ ServerConfigs loaded successfully');
  
  // Test the getEnabledDungeonServers function
  const enabledServers = serverConfigs.getEnabledDungeonServers();
  console.log(`📊 Found ${enabledServers.length} enabled dungeon servers:`);
  
  enabledServers.forEach((server, index) => {
    console.log(`\n  ${index + 1}. ${server.name} (${server.serverId})`);
    console.log(`     • Enabled: ${server.config.enabled}`);
    console.log(`     • Target Channel: ${server.config.targetChannelId}`);
    console.log(`     • Has Dungeon Roles: ${Object.keys(server.config.dungeonRoles).length > 0}`);
    console.log(`     • Has World Roles: ${Object.keys(server.config.worldRoles).length > 0}`);
    console.log(`     • Has Island Roles: ${server.config.islandRoles ? Object.keys(server.config.islandRoles).length > 0 : false}`);
  });
  
} catch (error) {
  console.error('❌ Error loading serverConfigs:', error);
  process.exit(1);
}

console.log('\n2️⃣ Testing module cache clearing...');
try {
  const configPath = path.resolve('./config/serverConfigs.js');
  console.log(`📁 Config file path: ${configPath}`);
  console.log(`📄 File exists: ${fs.existsSync(configPath)}`);
  
  // Test cache clearing
  const beforeCache = Object.keys(require.cache).filter(key => key.includes('serverConfigs')).length;
  delete require.cache[require.resolve('./config/serverConfigs.js')];
  const afterCache = Object.keys(require.cache).filter(key => key.includes('serverConfigs')).length;
  
  console.log(`🗑️ Cache entries before clearing: ${beforeCache}`);
  console.log(`🗑️ Cache entries after clearing: ${afterCache}`);
  console.log(`✅ Cache clearing: ${beforeCache > afterCache ? 'Working' : 'May have issues'}`);
  
} catch (error) {
  console.error('❌ Error testing cache clearing:', error);
}

console.log('\n3️⃣ Testing dungeon alert module loading...');
try {
  // Check if the dungeon alert module can load the config
  const DungeonAlert = require('./modules/dungeonAlert.js');
  console.log('✅ DungeonAlert module loaded successfully');
  
  // Test if we can create an instance (this will test config loading)
  const mockClient = { channels: { fetch: () => Promise.resolve(null) } };
  const dungeonAlert = new DungeonAlert(mockClient);
  console.log('✅ DungeonAlert instance created successfully');
  console.log(`📊 World Islands loaded: ${Object.keys(dungeonAlert.worldIslands).length} worlds`);
  console.log(`📊 Island Bosses loaded: ${Object.keys(dungeonAlert.islandBosses).length} islands`);
  
} catch (error) {
  console.error('❌ Error testing DungeonAlert module:', error);
}

console.log('\n4️⃣ Simulating dungeon alert sending...');
try {
  // Clear cache again to ensure fresh config
  delete require.cache[require.resolve('./config/serverConfigs.js')];
  const serverConfigs = require('./config/serverConfigs.js');
  
  const enabledServers = serverConfigs.getEnabledDungeonServers();
  console.log(`🎯 Would attempt to send alerts to ${enabledServers.length} servers:`);
  
  enabledServers.forEach((server, index) => {
    console.log(`\n  ${index + 1}. ${server.name}:`);
    console.log(`     • Server ID: ${server.serverId}`);
    console.log(`     • Channel ID: ${server.config.targetChannelId}`);
    console.log(`     • Config Object: ${typeof server.config}`);
    console.log(`     • Has Required Fields: ${!!(server.config.enabled && server.config.targetChannelId && server.config.dungeonRoles)}`);
    
    // Check if all required role IDs are present
    const requiredRoles = ['DUNGEON_PING', 'E', 'D', 'C', 'B', 'A', 'S', 'SS', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
    const missingRoles = requiredRoles.filter(role => !server.config.dungeonRoles[role]);
    
    if (missingRoles.length === 0) {
      console.log(`     ✅ All required dungeon roles present`);
    } else {
      console.log(`     ❌ Missing roles: ${missingRoles.join(', ')}`);
    }
    
    // Check world roles
    const worldRoles = ['1', '2'];
    const missingWorldRoles = worldRoles.filter(role => !server.config.worldRoles[role]);
    
    if (missingWorldRoles.length === 0) {
      console.log(`     ✅ All world roles present`);
    } else {
      console.log(`     ❌ Missing world roles: ${missingWorldRoles.join(', ')}`);
    }
  });
  
} catch (error) {
  console.error('❌ Error simulating alert sending:', error);
}

console.log('\n5️⃣ Testing specific server configuration...');
try {
  delete require.cache[require.resolve('./config/serverConfigs.js')];
  const serverConfigs = require('./config/serverConfigs.js');
  
  // Test the AI Server Maker specifically
  const aiServerConfig = serverConfigs.getDungeonConfig('1344361745019572314');
  
  if (aiServerConfig) {
    console.log('✅ AI Server Maker config found:');
    console.log(`   • Enabled: ${aiServerConfig.enabled}`);
    console.log(`   • Channel: ${aiServerConfig.targetChannelId}`);
    console.log(`   • Dungeon Roles: ${Object.keys(aiServerConfig.dungeonRoles).length}`);
    console.log(`   • World Roles: ${Object.keys(aiServerConfig.worldRoles).length}`);
    console.log(`   • Island Roles: ${aiServerConfig.islandRoles ? Object.keys(aiServerConfig.islandRoles).length : 'None'}`);
  } else {
    console.log('❌ AI Server Maker config not found');
  }
  
  // Test the main server for comparison
  const mainServerConfig = serverConfigs.getDungeonConfig('1362356687092191442');
  
  if (mainServerConfig) {
    console.log('\n✅ RankBreaker Main Server config found:');
    console.log(`   • Enabled: ${mainServerConfig.enabled}`);
    console.log(`   • Channel: ${mainServerConfig.targetChannelId}`);
    console.log(`   • Dungeon Roles: ${Object.keys(mainServerConfig.dungeonRoles).length}`);
    console.log(`   • World Roles: ${Object.keys(mainServerConfig.worldRoles).length}`);
    console.log(`   • Island Roles: ${mainServerConfig.islandRoles ? Object.keys(mainServerConfig.islandRoles).length : 'None'}`);
  }
  
} catch (error) {
  console.error('❌ Error testing specific server config:', error);
}

console.log('\n6️⃣ Checking for potential issues...');
try {
  delete require.cache[require.resolve('./config/serverConfigs.js')];
  const serverConfigs = require('./config/serverConfigs.js');
  
  const enabledServers = serverConfigs.getEnabledDungeonServers();
  
  console.log('🔍 Potential Issues Check:');
  
  // Check if the bot is actually in the servers
  console.log('\n📋 Server Access Check:');
  enabledServers.forEach(server => {
    console.log(`   • ${server.name}: Channel ID ${server.config.targetChannelId}`);
    console.log(`     - Bot needs to be in server ${server.serverId}`);
    console.log(`     - Bot needs access to channel ${server.config.targetChannelId}`);
    console.log(`     - Bot needs permission to send messages in that channel`);
  });
  
  // Check if there are any syntax issues in the config
  console.log('\n🔧 Configuration Validation:');
  enabledServers.forEach(server => {
    const issues = [];
    
    if (!server.config.enabled) issues.push('Not enabled');
    if (!server.config.targetChannelId) issues.push('No target channel');
    if (!server.config.dungeonRoles || Object.keys(server.config.dungeonRoles).length === 0) issues.push('No dungeon roles');
    if (!server.config.worldRoles || Object.keys(server.config.worldRoles).length === 0) issues.push('No world roles');
    
    if (issues.length === 0) {
      console.log(`   ✅ ${server.name}: No configuration issues found`);
    } else {
      console.log(`   ❌ ${server.name}: ${issues.join(', ')}`);
    }
  });
  
} catch (error) {
  console.error('❌ Error checking for issues:', error);
}

console.log('\n🎯 Debug Summary');
console.log('================');
console.log('Check the output above for any ❌ errors.');
console.log('Common issues:');
console.log('1. Bot not in the target server');
console.log('2. Bot missing permissions in target channel');
console.log('3. Target channel ID incorrect');
console.log('4. Module cache not clearing properly');
console.log('5. Configuration syntax errors');
console.log('\nIf all checks show ✅, the issue might be:');
console.log('- Bot needs to be restarted to pick up config changes');
console.log('- Dungeon monitoring system not running');
console.log('- Time window restrictions preventing alerts');
