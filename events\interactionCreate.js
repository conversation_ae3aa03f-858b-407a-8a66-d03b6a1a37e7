const { isOwner } = require('../utils/ownerCheck');
const config = require('../config/config.js')

module.exports = {
    name: 'interactionCreate',
    async execute(interaction) {
        // Handle different interaction types separately
        if (interaction.isCommand()) {
            await handleCommandInteraction(interaction);
        } else if (interaction.isStringSelectMenu()) {
            await handleSelectMenuInteraction(interaction);
        } else if (interaction.isButton()) {
            await handleButtonInteraction(interaction);
        }
    },
};

// Handle slash commands
async function handleCommandInteraction(interaction) {
    const command = interaction.client.slashCommands.get(interaction.commandName);
    if (!command) return;

    try {
        // Make sure we're in a guild context before accessing guild-specific properties
        if (command.guildOnly && !interaction.guild) {
            return interaction.reply({ content: 'This command can only be used in a server.', ephemeral: true });
        }

        // Owner-only check
        if (command.OwnerOnly && !isOwner(interaction.user.id)) {
            return interaction.reply({ content: config.ResponsesConfig.botowneronly.reply, ephemeral: true });
        }

        // Server owner-only check - Make sure guild exists before checking ownerId
        if (command.ServerOwnerOnly && interaction.guild && interaction.user.id !== interaction.guild.ownerId) {
            return interaction.reply({ content: config.ResponsesConfig.serverowneronly.reply, ephemeral: true });
        }

        // Developer Team-only check
        function isDeveloperTeamMember(user, guild) {
            if (config.developerTeam.memberIDs.includes(user.id)) return true;
            if (!guild) return false; // Skip role check if not in a guild
            const member = guild.members.cache.get(user.id);
            if (member && config.developerTeam.roleIDs.some(roleID => member.roles.cache.has(roleID))) {
                return true;
            }
            return false;
        }

        if (command.DeveloperTeamOnly && !isDeveloperTeamMember(interaction.user, interaction.guild)) {
            return interaction.reply({ content: config.ResponsesConfig.developerteamonly.reply, ephemeral: true });
        }

        // NSFW check - Make sure channel exists and has nsfw property
        if (command.nsfw && interaction.channel && !interaction.channel.nsfw) {
            return interaction.reply({ content: config.ResponsesConfig.nsfw.reply, ephemeral: true });
        }

        // Permissions check - Make sure we're in a guild context
        if (interaction.guild) {
            if (command.memberpermissions && command.memberpermissions.length > 0 && 
                !interaction.member.permissions.has(command.memberpermissions)) {
                return interaction.reply({ content: config.ResponsesConfig.memberpermissions.reply, ephemeral: true });
            }

            if (command.botpermissions && command.botpermissions.length > 0 && 
                !interaction.guild.members.me.permissions.has(command.botpermissions)) {
                return interaction.reply({ content: config.ResponsesConfig.botpermissions.reply, ephemeral: true });
            }
        }

        // Required roles check - Make sure we're in a guild context
        if (interaction.guild && command.requiredroles && command.requiredroles.length > 0 && 
            !command.requiredroles.some(role => interaction.member.roles.cache.has(role))) {
            return interaction.reply({ content: config.ResponsesConfig.requiredroles.reply, ephemeral: true });
        }

        // Required channels check
        if (command.requiredchannels && command.requiredchannels.length > 0 && 
            !command.requiredchannels.includes(interaction.channelId)) {
            return interaction.reply({ content: config.ResponsesConfig.requiredchannels.reply, ephemeral: true });
        }

        // Allowed users check
        if (command.alloweduserids && command.alloweduserids.length > 0 && 
            !command.alloweduserids.includes(interaction.user.id)) {
            return interaction.reply({ content: config.ResponsesConfig.alloweduserids.reply, ephemeral: true });
        }

        // Cooldown check
        if (!interaction.client.cooldowns) interaction.client.cooldowns = new Map();
        const now = Date.now();
        const timestamps = interaction.client.cooldowns.get(command.name) || new Map();
        const cooldownAmount = (command.cooldown || config.CooldownConfig.defaultCooldown) * 1000;

        if (timestamps.has(interaction.user.id)) {
            const expirationTime = timestamps.get(interaction.user.id) + cooldownAmount;
            if (now < expirationTime) {
                const timeLeft = (expirationTime - now) / 1000;
                return interaction.reply({ content: `Please wait ${timeLeft.toFixed(1)} more seconds before using \`${command.name}\` again.`, ephemeral: true });
            }
        }

        timestamps.set(interaction.user.id, now);
        interaction.client.cooldowns.set(command.name, timestamps);

        // Execute the command
        await command.execute(interaction);
    } catch (error) {
        console.error(`Error executing command ${interaction.commandName}:`, error);
        try {
            // Check if the interaction has already been replied to
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error executing that command.', ephemeral: true });
            } else {
                await interaction.reply({ content: 'There was an error executing that command.', ephemeral: true });
            }
        } catch (replyError) {
            console.error('Error sending error reply:', replyError);
        }
    }
}

// Handle button interactions
async function handleButtonInteraction(interaction) {
    const fs = require('fs');
    const path = require('path');
    const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    try {
        // Handle giveaway button interactions
        if (interaction.customId.startsWith('giveaway_enter_')) {
            const giveawayId = interaction.customId.replace('giveaway_enter_', '');
            const giveawayFile = path.join(__dirname, '../data/giveaways.json');

            // Read current giveaway data
            let currentGiveaways = {};
            if (fs.existsSync(giveawayFile)) {
                try {
                    currentGiveaways = JSON.parse(fs.readFileSync(giveawayFile, 'utf8'));
                } catch (error) {
                    currentGiveaways = {};
                }
            }

            if (!currentGiveaways[giveawayId] || !currentGiveaways[giveawayId].active) {
                return interaction.reply({
                    content: '❌ This giveaway is no longer active!',
                    ephemeral: true
                });
            }

            const userId = interaction.user.id;

            // Check if user already entered
            if (currentGiveaways[giveawayId].entries.includes(userId)) {
                return interaction.reply({
                    content: '⚠️ You have already entered this giveaway!',
                    ephemeral: true
                });
            }

            // Add user to entries
            currentGiveaways[giveawayId].entries.push(userId);
            fs.writeFileSync(giveawayFile, JSON.stringify(currentGiveaways, null, 2));

            // Update embed
            const updatedEmbed = new EmbedBuilder()
                .setTitle('🎉 RankBreaker\'s Giveaway 🎉')
                .setDescription(`**Prize:** ${currentGiveaways[giveawayId].prize}\n\n**How to Enter:**\nClick the 🎁 button below to enter!\n\n**Entries:** ${currentGiveaways[giveawayId].entries.length}\n\n**Status:** Active`)
                .setColor('#3498db')
                .setFooter({ text: `Giveaway ID: ${giveawayId}`, iconURL: interaction.client.user.displayAvatarURL() })
                .setTimestamp()
                .setThumbnail('https://cdn.discordapp.com/emojis/742043142750388304.png');

            // Recreate the button
            const button = new ButtonBuilder()
                .setCustomId(`giveaway_enter_${giveawayId}`)
                .setLabel('🎁 Enter Giveaway')
                .setStyle(ButtonStyle.Primary);

            const row = new ActionRowBuilder().addComponents(button);

            await interaction.update({ embeds: [updatedEmbed], components: [row] });

            // Send success message
            await interaction.followUp({
                content: '🎉 You have successfully entered the giveaway! Good luck!',
                ephemeral: true
            });
        }
        // Handle other button interactions from other modules
        else if (interaction.customId === 'dismiss_error' ||
                 interaction.customId === 'undo_role_add' ||
                 interaction.customId === 'approve_anyway' ||
                 interaction.customId === 'retry_action') {
            // Pass to screenshot verification module if it exists
            if (interaction.client.screenshotVerification) {
                await interaction.client.screenshotVerification.handleButtonInteraction(interaction);
            }
        }
    } catch (error) {
        console.error('Error handling button interaction:', error);
        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error processing your request.', ephemeral: true });
            } else {
                await interaction.reply({ content: 'There was an error processing your request.', ephemeral: true });
            }
        } catch (replyError) {
            console.error('Error sending error reply:', replyError);
        }
    }
}

// Handle select menus
async function handleSelectMenuInteraction(interaction) {
    // Handle ping roles menu
    if (interaction.customId === 'ping-roles-menu') {
        // Defer reply as ephemeral to avoid timeouts
        await interaction.deferReply({ ephemeral: true });
        
        try {
            const member = interaction.member;
            const selectedValues = interaction.values;
            const guild = interaction.guild;
            
            // Define role IDs - these should match the ones in self-roles.js
            const ROLE_IDS = {
                'dungeons_ping': '1364559489604390996',
                'double_dungeon_ping': '1364559627144003645',
                'red_gate_ping': '1364559668772605992',
                'ss_dungeon_ping': '1364559909722652723',
                's_dungeon_ping': '1364560060923252766',
                'a_dungeon_ping': '1364560068133126144',
                'b_dungeon_ping': '1364560075770826782',
                'c_dungeon_ping': '1364560082519588875',
                'd_dungeon_ping': '1364560380709306389',
                'e_dungeon_ping': '1364560442910969866'
            };

            // Role names for the response message
            const ROLE_NAMES = {
                'dungeons_ping': 'Dungeons Ping',
                'double_dungeon_ping': 'Double Dungeon Ping',
                'red_gate_ping': 'Red Gate Ping',
                'ss_dungeon_ping': 'SS Dungeon Ping',
                's_dungeon_ping': 'S Dungeon Ping',
                'a_dungeon_ping': 'A Dungeon Ping',
                'b_dungeon_ping': 'B Dungeon Ping',
                'c_dungeon_ping': 'C Dungeon Ping',
                'd_dungeon_ping': 'D Dungeon Ping',
                'e_dungeon_ping': 'E Dungeon Ping'
            };
            
            // Track roles added and removed for the response message
            const rolesAdded = [];
            const rolesRemoved = [];
            
            // Get all available ping roles
            const allRoleKeys = Object.keys(ROLE_IDS);
            
            // Process each role
            for (const roleKey of allRoleKeys) {
                const roleId = ROLE_IDS[roleKey];
                const role = await guild.roles.fetch(roleId).catch(console.error);
                
                if (!role) continue; // Skip if role doesn't exist
                
                // Check if the role should be added or removed
                if (selectedValues.includes(roleKey)) {
                    // User selected this role - add it if they don't have it
                    if (!member.roles.cache.has(roleId)) {
                        await member.roles.add(role);
                        rolesAdded.push(ROLE_NAMES[roleKey]);
                    }
                } else {
                    // User did not select this role - remove it if they have it
                    if (member.roles.cache.has(roleId)) {
                        await member.roles.remove(role);
                        rolesRemoved.push(ROLE_NAMES[roleKey]);
                    }
                }
            }
            
            // Create response message
            let responseMessage = '';
            
            if (rolesAdded.length > 0) {
                responseMessage += `**Added roles:**\n${rolesAdded.map(r => `• ${r}`).join('\n')}\n\n`;
            }
            
            if (rolesRemoved.length > 0) {
                responseMessage += `**Removed roles:**\n${rolesRemoved.map(r => `• ${r}`).join('\n')}`;
            }
            
            if (!responseMessage) {
                responseMessage = 'No changes were made to your roles.';
            }
            
            // Reply with the result
            await interaction.editReply({
                content: responseMessage,
                ephemeral: true
            });
            
        } catch (error) {
            console.error('Error handling role selection:', error);
            await interaction.editReply({
                content: 'There was an error processing your role selection. Please try again later.',
                ephemeral: true
            });
        }
    }
    
    // Handle island roles menu
    else if (interaction.customId === 'island-roles-menu') {
        // Defer reply as ephemeral to avoid timeouts
        await interaction.deferReply({ ephemeral: true });
        
        try {
            const member = interaction.member;
            const selectedValues = interaction.values;
            const guild = interaction.guild;
            
            // Define role IDs for island roles
            const ISLAND_ROLE_IDS = {
                'leveling_city': '1371844586003103865',
                'grass_village': '1371844603040370759',
                'brum_island': '1371844612196532304',
                'faceheal_town': '1371844622015402086',
                'lucky_kingdom': '1371844631092003037',
                'nipon_city': '1371844639430021132',
                'mori_town': '1371844649798602833',
                'dragon_city': '1371844984244015256',
                'xz_city': '1371845004598968441',
                'kindama_city': '1371845011116785674',
                'hunter_city': '1373302432389398559',
                'nen_city': '1380528415031033886' // Adding Nen City role ID
            };

            // Role names for the response message
            const ISLAND_ROLE_NAMES = {
                'leveling_city': 'Leveling City',
                'grass_village': 'Grass Village',
                'brum_island': 'Brum Island',
                'faceheal_town': 'Faceheal Town',
                'lucky_kingdom': 'Lucky Kingdom',
                'nipon_city': 'Nipon City',
                'mori_town': 'Mori Town',
                'dragon_city': 'Dragon City',
                'xz_city': 'XZ City',
                'kindama_city': 'Kindama City',
                'hunter_city': 'Hunter City',
                'nen_city': 'Nen City'
            };
            
            // Track roles added and removed for the response message
            const rolesAdded = [];
            const rolesRemoved = [];
            
            // Get all available island roles
            const allRoleKeys = Object.keys(ISLAND_ROLE_IDS);
            
            // Process each role
            for (const roleKey of allRoleKeys) {
                const roleId = ISLAND_ROLE_IDS[roleKey];
                const role = await guild.roles.fetch(roleId).catch(console.error);
                
                if (!role) continue; // Skip if role doesn't exist
                
                // Check if the role should be added or removed
                if (selectedValues.includes(roleKey)) {
                    // User selected this role - add it if they don't have it
                    if (!member.roles.cache.has(roleId)) {
                        await member.roles.add(role);
                        rolesAdded.push(ISLAND_ROLE_NAMES[roleKey]);
                    }
                } else {
                    // User did not select this role - remove it if they have it
                    if (member.roles.cache.has(roleId)) {
                        await member.roles.remove(role);
                        rolesRemoved.push(ISLAND_ROLE_NAMES[roleKey]);
                    }
                }
            }
            
            // Create response message
            let responseMessage = '';
            
            if (rolesAdded.length > 0) {
                responseMessage += `**Added roles:**\n${rolesAdded.map(r => `• ${r}`).join('\n')}\n\n`;
            }
            
            if (rolesRemoved.length > 0) {
                responseMessage += `**Removed roles:**\n${rolesRemoved.map(r => `• ${r}`).join('\n')}`;
            }
            
            if (!responseMessage) {
                responseMessage = 'No changes were made to your roles.';
            }
            
            // Reply with the result
            await interaction.editReply({
                content: responseMessage,
                ephemeral: true
            });
            
        } catch (error) {
            console.error('Error handling island role selection:', error);
            await interaction.editReply({
                content: 'There was an error processing your island role selection. Please try again later.',
                ephemeral: true
            });
        }
    }
    
    // Handle infernal roles menu
    else if (interaction.customId === 'infernal-roles-menu') {
        // Defer reply as ephemeral to avoid timeouts
        await interaction.deferReply({ ephemeral: true });
        
        try {
            const member = interaction.member;
            const selectedValues = interaction.values;
            const guild = interaction.guild;
            
            // Define role IDs for infernal roles
            const INFERNAL_ROLE_IDS = {
                'infernal_ping': '1372921375005737003',
                'monarch_ping': '1373306924115824740',
                'dae_in_ping': '1374780329864204330'
            };
            
            // Role names for the response message
            const INFERNAL_ROLE_NAMES = {
                'infernal_ping': 'Infernal Ping',
                'monarch_ping': 'Monarch Ping',
                'dae_in_ping': 'Dae In Ping'
            };
            
            // Track roles added and removed for the response message
            const rolesAdded = [];
            const rolesRemoved = [];
            
            // Get all available infernal roles
            const allRoleKeys = Object.keys(INFERNAL_ROLE_IDS);
            
            // Process each role
            for (const roleKey of allRoleKeys) {
                const roleId = INFERNAL_ROLE_IDS[roleKey];
                const role = await guild.roles.fetch(roleId).catch(console.error);
                
                if (!role) continue; // Skip if role doesn't exist
                
                // Check if the role should be added or removed
                if (selectedValues.includes(roleKey)) {
                    // User selected this role - add it if they don't have it
                    if (!member.roles.cache.has(roleId)) {
                        await member.roles.add(role);
                        rolesAdded.push(INFERNAL_ROLE_NAMES[roleKey]);
                    }
                } else {
                    // User did not select this role - remove it if they have it
                    if (member.roles.cache.has(roleId)) {
                        await member.roles.remove(role);
                        rolesRemoved.push(INFERNAL_ROLE_NAMES[roleKey]);
                    }
                }
            }
            
            // Create response message
            let responseMessage = '';
            
            if (rolesAdded.length > 0) {
                responseMessage += `**Added roles:**\n${rolesAdded.map(r => `• ${r}`).join('\n')}\n\n`;
            }
            
            if (rolesRemoved.length > 0) {
                responseMessage += `**Removed roles:**\n${rolesRemoved.map(r => `• ${r}`).join('\n')}`;
            }
            
            if (!responseMessage) {
                responseMessage = 'No changes were made to your roles.';
            }
            
            // Reply with the result
            await interaction.editReply({
                content: responseMessage,
                ephemeral: true
            });
            
        } catch (error) {
            console.error('Error handling infernal role selection:', error);
            await interaction.editReply({
                content: 'There was an error processing your infernal role selection. Please try again later.',
                ephemeral: true
            });
        }
    }

    // Handle World 1 roles menu
    else if (interaction.customId === 'world1-roles-menu') {
        // Defer reply as ephemeral to avoid timeouts
        await interaction.deferReply({ ephemeral: true });

        try {
            const member = interaction.member;
            const selectedValues = interaction.values;
            const guild = interaction.guild;

            // Define role IDs for World 1 roles
            const WORLD1_ROLE_IDS = {
                'world_1_ping': '1380529138309529762',
                'leveling_city': '1371844586003103865',
                'grass_village': '1371844603040370759',
                'brum_island': '1371844612196532304',
                'faceheal_town': '1371844622015402086',
                'lucky_kingdom': '1371844631092003037',
                'nipon_city': '1371844639430021132',
                'mori_town': '1371844649798602833'
            };

            // Role names for the response message
            const WORLD1_ROLE_NAMES = {
                'world_1_ping': 'World 1 Ping',
                'leveling_city': 'Leveling City',
                'grass_village': 'Grass Village',
                'brum_island': 'Brum Island',
                'faceheal_town': 'Faceheal Town',
                'lucky_kingdom': 'Lucky Kingdom',
                'nipon_city': 'Nipon City',
                'mori_town': 'Mori Town'
            };

            // Track roles added and removed for the response message
            const rolesAdded = [];
            const rolesRemoved = [];

            // Get all available World 1 roles
            const allRoleKeys = Object.keys(WORLD1_ROLE_IDS);

            // Process each role
            for (const roleKey of allRoleKeys) {
                const roleId = WORLD1_ROLE_IDS[roleKey];
                const role = await guild.roles.fetch(roleId).catch(console.error);

                if (!role) continue; // Skip if role doesn't exist

                // Check if the role should be added or removed
                if (selectedValues.includes(roleKey)) {
                    // User selected this role - add it if they don't have it
                    if (!member.roles.cache.has(roleId)) {
                        await member.roles.add(role);
                        rolesAdded.push(WORLD1_ROLE_NAMES[roleKey]);
                    }
                } else {
                    // User did not select this role - remove it if they have it
                    if (member.roles.cache.has(roleId)) {
                        await member.roles.remove(role);
                        rolesRemoved.push(WORLD1_ROLE_NAMES[roleKey]);
                    }
                }
            }

            // Create response message
            let responseMessage = '';

            if (rolesAdded.length > 0) {
                responseMessage += `**Added roles:**\n${rolesAdded.map(r => `• ${r}`).join('\n')}\n\n`;
            }

            if (rolesRemoved.length > 0) {
                responseMessage += `**Removed roles:**\n${rolesRemoved.map(r => `• ${r}`).join('\n')}`;
            }

            if (!responseMessage) {
                responseMessage = 'No changes were made to your roles.';
            }

            // Reply with the result
            await interaction.editReply({
                content: responseMessage,
                ephemeral: true
            });

        } catch (error) {
            console.error('Error handling World 1 role selection:', error);
            await interaction.editReply({
                content: 'There was an error processing your World 1 role selection. Please try again later.',
                ephemeral: true
            });
        }
    }

    // Handle World 2 roles menu
    else if (interaction.customId === 'world2-roles-menu') {
        // Defer reply as ephemeral to avoid timeouts
        await interaction.deferReply({ ephemeral: true });

        try {
            const member = interaction.member;
            const selectedValues = interaction.values;
            const guild = interaction.guild;

            // Define role IDs for World 2 roles
            const WORLD2_ROLE_IDS = {
                'world_2_ping': '1380529145938972712',
                'dragon_city': '1371844984244015256',
                'xz_city': '1371845004598968441',
                'kindama_city': '1371845011116785674',
                'hunter_city': '1373302432389398559',
                'nen_city': '1380528415031033886',
            };

            // Role names for the response message
            const WORLD2_ROLE_NAMES = {
                'world_2_ping': 'World 2 Ping',
                'dragon_city': 'Dragon City',
                'xz_city': 'XZ City',
                'kindama_city': 'Kindama City',
                'hunter_city': 'Hunters City',
                'nen_city': 'Nen City',
            };

            // Track roles added and removed for the response message
            const rolesAdded = [];
            const rolesRemoved = [];

            // Get all available World 2 roles
            const allRoleKeys = Object.keys(WORLD2_ROLE_IDS);

            // Process each role
            for (const roleKey of allRoleKeys) {
                const roleId = WORLD2_ROLE_IDS[roleKey];
                const role = await guild.roles.fetch(roleId).catch(console.error);

                if (!role) continue; // Skip if role doesn't exist

                // Check if the role should be added or removed
                if (selectedValues.includes(roleKey)) {
                    // User selected this role - add it if they don't have it
                    if (!member.roles.cache.has(roleId)) {
                        await member.roles.add(role);
                        rolesAdded.push(WORLD2_ROLE_NAMES[roleKey]);
                    }
                } else {
                    // User did not select this role - remove it if they have it
                    if (member.roles.cache.has(roleId)) {
                        await member.roles.remove(role);
                        rolesRemoved.push(WORLD2_ROLE_NAMES[roleKey]);
                    }
                }
            }

            // Create response message
            let responseMessage = '';

            if (rolesAdded.length > 0) {
                responseMessage += `**Added roles:**\n${rolesAdded.map(r => `• ${r}`).join('\n')}\n\n`;
            }

            if (rolesRemoved.length > 0) {
                responseMessage += `**Removed roles:**\n${rolesRemoved.map(r => `• ${r}`).join('\n')}`;
            }

            if (!responseMessage) {
                responseMessage = 'No changes were made to your roles.';
            }

            // Reply with the result
            await interaction.editReply({
                content: responseMessage,
                ephemeral: true
            });

        } catch (error) {
            console.error('Error handling World 2 role selection:', error);
            await interaction.editReply({
                content: 'There was an error processing your World 2 role selection. Please try again later.',
                ephemeral: true
            });
        }
    }
}